/**
 * This class contains unit tests for validating the behavior of Apex classes
 * and triggers.
 *
 * Unit tests are class methods that verify whether a particular piece
 * of code is working properly. Unit test methods take no arguments,
 * commit no data to the database, and are flagged with the testMethod
 * keyword in the method definition.
 *
 * All test methods in an organization are executed whenever Apex code is deployed
 * to a production organization to confirm correctness, ensure code
 * coverage, and prevent regressions. All Apex classes are
 * required to have at least 75% code coverage in order to be deployed
 * to a production organization. In addition, all triggers must have some code coverage.
 * 
 * The @isTest class annotation indicates this class only contains test
 * methods. Classes defined with the @isTest annotation do not count against
 * the organization size limit for all Apex scripts.
 *
 * See the Apex Language Reference for more information about Testing and Code Coverage.
 */
@isTest
private class QISTriggerTest {
    
    @TestVisible
    private static Boolean isTest = false;

    private static String MOCK_RESPONSE = '{"success": true}';
    
    // HTTP 调用的 Mock 类
    private class MockHttpResponseGenerator implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HTTPResponse res = new HTTPResponse();
            res.setStatus('OK');
            res.setStatusCode(200);
            res.setBody(MOCK_RESPONSE);
            return res;
        }
    }

    @testSetup 
    static void setup() {
        // 设置测试标记
        isTest = true;
        
        User testUser = new User(
            Test_staff__c = true, 
            LastName = 'TestUser',
            Alias = 'tuser',
            Email = '<EMAIL>',
            Username = '<EMAIL>' + System.currentTimeMillis(),
            CommunityNickname = 'TestUser' + System.currentTimeMillis(),
            EmailEncodingKey = 'ISO-2022-JP',
            TimeZoneSidKey = 'Asia/Tokyo',
            LocaleSidKey = 'ja_JP',
            LanguageLocaleKey = 'ja',
            ProfileId = UserInfo.getProfileId(),
            Dept__c = '医疗华北营业本部',
            Province__c = '北京市',
            Job_Category__c = '销售服务'
        );
        insert testUser;

    }

    @isTest
    static void testQISInsertAndUpdate() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];
        
        // 创建测试日历数据
        List<OlympusCalendar__c> calendarList = new List<OlympusCalendar__c>();
        for(Integer i = 0; i < 3; i++) {
            calendarList.add(new OlympusCalendar__c(Date__c = Date.today().addDays(i)));
        }
        insert calendarList;
        
        // 创建测试产品
        Product2 testProduct = new Product2(
            Name = 'Test Product',
            IsActive = true,
            Fixture_Model_No__c = 'TEST-001',
            Serial_Lot_No__c = 'S/N tracing',
            Fixture_Model_No_T__c = 'TEST-001',
            Category2__c = '耗材',
            Family = 'ENG',
            ProductCode_Ext__c = 'TEST001',
            Manual_Entry__c = false
        );
        insert testProduct;
        
        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-001',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );
            
            Test.startTest();
            insert testQIS;
            
            // 测试更新其他字段
            testQIS = [SELECT Id FROM QIS_Report__c WHERE Id = :testQIS.Id];
            testQIS.Damage_For_Doc_Or_Pat__c = '无';
            testQIS.Relation_With_The_Problem__c = '不知道';
            update testQIS;
            Test.stopTest();
        }
    }

    @isTest
    static void testQISWithAsset() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];
        
        // 创建测试产品
        Product2 testProduct = new Product2(
            Name = 'Test Product',
            IsActive = true,
            Fixture_Model_No__c = 'TEST-001',
            Serial_Lot_No__c = 'S/N tracing',
            Fixture_Model_No_T__c = 'TEST-001',
            Category2__c = '耗材',
            Family = 'ENG',
            ProductCode_Ext__c = 'TEST001',
            Manual_Entry__c = false
        );
        insert testProduct;

        // 创建测试医院数据
        Account testHospital = new Account(
            RecordTypeId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' 
                          AND DeveloperName = 'HP' LIMIT 1].Id,
            Name = 'Test Hospital',
            Is_Active__c = '有效',
            Attribute_Type__c = '卫生部',
            Speciality_Type__c = '综合医院',
            Grade__c = '一级',
            OCM_Category__c = 'SLTV',
            Is_Medical__c = '医疗机构',
            Department_Class__c = null
        );
        insert testHospital;

        // 查询默认创建的战略科室
        List<Account> strategicDepts = [
            SELECT Id, Name, Attribute_Type__c, Speciality_Type__c, Grade__c, OCM_Category__c, Is_Medical__c 
            FROM Account 
            WHERE ParentId = :testHospital.Id 
            AND RecordType.DeveloperName = 'Department_Class_OTH' 
            LIMIT 1
        ];

        Account strategicDepartment;
        if(!strategicDepts.isEmpty()) {
            strategicDepartment = strategicDepts[0];
        } else {
            // 如果没有默认战略科室，创建一个新的
            strategicDepartment = new Account(
                RecordTypeId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' 
                              AND DeveloperName = 'Department_Class_OTH' LIMIT 1].Id,
                Name = 'Strategic Department',
                ParentId = testHospital.Id,
                Attribute_Type__c = testHospital.Attribute_Type__c,
                Speciality_Type__c = testHospital.Speciality_Type__c,
                Grade__c = testHospital.Grade__c,
                OCM_Category__c = testHospital.OCM_Category__c,
                Is_Medical__c = testHospital.Is_Medical__c
            );
            insert strategicDepartment;
        }

        // 创建测试科室，确保所有分类字段与战略科室完全一致
        Account testDepartment = new Account(
            RecordTypeId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' 
                          AND DeveloperName = 'Department_OTH' LIMIT 1].Id,
            Name = 'Test Department',
            ParentId = strategicDepartment.Id,
            Department_Class__c = strategicDepartment.Id,
            Hospital__c = testHospital.Id,
            Attribute_Type__c = strategicDepartment.Attribute_Type__c,
            Speciality_Type__c = strategicDepartment.Speciality_Type__c,
            Grade__c = strategicDepartment.Grade__c,
            OCM_Category__c = strategicDepartment.OCM_Category__c,
            Is_Medical__c = strategicDepartment.Is_Medical__c
        );
        insert testDepartment;

        // 创建测试资产
        Asset testAsset = new Asset(
            Name = 'Test Asset',
            Asset_Owner__c = 'Olympus',
            SerialNumber = 'TEST-SERIAL-001',
            AccountId = testDepartment.Id,
            Hospital__c = testHospital.Id,
            Product2Id = testProduct.Id,
            Quantity = 1,
            Status = '有库存',
            Manage_type__c = '个体管理',
            Internal_asset_location__c = '北京 备品中心',
            Delete_Flag__c = false,
            Freeze_sign__c = false,
            Out_of_wh__c = 0
        );
        insert testAsset;

        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Photo_1__c = '<img src="http://test.com/img1.jpg" />',
                Name = 'QIS-TEST-002',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                nonyushohin__c = testAsset.Id  // 关联测试资产
            );

            Test.startTest();
            
            // 测试插入带资产的QIS
            insert testQIS;
            
            // 更新资产类别
            testProduct.Category2__c = '内窥镜';
            testProduct.Family = 'GI';
            update testProduct;
            
            // 更新QIS报告
            testQIS.Photo_1__c = '<img src="http://test.com/img1_new.jpg" />';
            testQIS.Damage_For_Doc_Or_Pat__c = '有';
            testQIS.Relation_With_The_Problem__c = '有可能';
            testQIS.Report_For_Goz__c = '有';  // 使用正确的选择列表值
            update testQIS;

            // 验证更新结果
            testQIS = [SELECT Id, Damage_For_Doc_Or_Pat__c, Relation_With_The_Problem__c, Report_For_Goz__c
                      FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals('有', testQIS.Damage_For_Doc_Or_Pat__c);
            System.assertEquals('有可能', testQIS.Relation_With_The_Problem__c);
            System.assertEquals('有', testQIS.Report_For_Goz__c);
            
            // 测试解除资产关联
            testQIS.nonyushohin__c = null;
            update testQIS;

            // 验证资产关联已解除
            testQIS = [SELECT Id, nonyushohin__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals(null, testQIS.nonyushohin__c, 'Asset should be unlinked');
            
            Test.stopTest();
        }
    }

    @isTest
    static void testQISWithEmailNotification() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];
        
        // 设置HTTP Mock
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
        
        System.runAs(testUser) {
            Test.startTest();
            
            // 创建带邮件通知地址的QIS记录
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-003',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                contract_number_ET__c = 'TEST-CONTRACT-003',
                ET_QIS_SEND_EMAIL1__c = '<EMAIL>',
                ET_QIS_SEND_EMAIL2__c = '<EMAIL>',
                ET_QIS_SEND_EMAIL3__c = '<EMAIL>',
                ET_QIS_SEND_EMAIL4__c = '<EMAIL>',
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道',
                QIS_Status__c = '新規作成'
            );
            
            insert testQIS;
            System.assertNotEquals(null, testQIS.Id, 'QIS record should be created');
            
            // 修改状态触发邮件通知
            testQIS.QIS_Status__c = '申請中';
            testQIS.Damage_For_Doc_Or_Pat__c = '无';
            testQIS.Relation_With_The_Problem__c = '不知道';
            update testQIS;
            
            // 进一步更新状态触发第二次邮件通知
            testQIS.QIS_Status__c = '承認済';
            update testQIS;
            
            Test.stopTest();
            
            // 验证最终状态和字段值
            testQIS = [SELECT Id, QIS_Status__c, Damage_For_Doc_Or_Pat__c, Relation_With_The_Problem__c
                      FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals('承認済', testQIS.QIS_Status__c, 'QIS Status should be updated to 承認済');
            System.assertEquals('无', testQIS.Damage_For_Doc_Or_Pat__c, 'Damage field should be updated');
            System.assertEquals('不知道', testQIS.Relation_With_The_Problem__c, 'Relation field should be updated');
        }
    }
    
    @isTest
    static void testQISComprehensive() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];
        
        // 获取QIS记录类型
        String qisRecordTypeId = Schema.SObjectType.QIS_Report__c.getRecordTypeInfosByDeveloperName()
            .get('QISRecordType1').getRecordTypeId();
            
        System.runAs(testUser) {
            // 创建测试QIS记录
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-004',
                RecordTypeId = qisRecordTypeId,
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                QIS_Status__c = '新規作成',
                // OCSM相关字段
                OCSMAdministrativeReportStatus__c = '无需报告',
                OCSMAdministrativeReportNumber__c = 'TEST-OCSM-001',
                OCSMAdministrativeReportDate__c = Date.today(),
                isAE_Profile__c = 'YES',
                isPAE_Profile__c = 'YES',
                next_action__c = '无偿维修',
                // 其他必填字段
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '有'
            );
            
            Test.startTest();
            
            // 插入记录
            insert testQIS;
            System.assertNotEquals(null, testQIS.Id, 'QIS record should be created');
            
            // 更新OCSM相关信息
            testQIS.OCSMAdministrativeReportStatus__c = '已报告';
            testQIS.OCSMAdministrativeReportDate__c = Date.today().addDays(1);
            update testQIS;
            
            // 更新状态以触发租借申请标记
            testQIS.isLendRental__c = true;
            testQIS.QIS_Status__c = '申請中';
            update testQIS;
            
            // 最后更新为提交状态
            testQIS.QIS_Status__c = '提出済';
            update testQIS;
            
            Test.stopTest();
            
            // 验证最终记录状态
            testQIS = [SELECT Id, QIS_Status__c, OCSMAdministrativeReportStatus__c, isLendRental__c 
                      FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals('提出済', testQIS.QIS_Status__c, 'QIS Status should be updated to 提出済');
            System.assertEquals('已报告', testQIS.OCSMAdministrativeReportStatus__c, 'OCSM status should be updated');
            System.assertEquals(true, testQIS.isLendRental__c, 'Rental flag should be set');
        }
    }
    
    @isTest
    static void testQISWithHTTPCallout() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        // 设置HTTP Mock
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());

        System.runAs(testUser) {
            Test.startTest();

                        // 创建需要触发HTTP回调的QIS记录
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-HTTP',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道',
                QIS_Status__c = '新規作成'
            );

            insert testQIS;

            // 更新状态以触发HTTP回调
            testQIS.QIS_Status__c = '提出済';
            update testQIS;

            // 再次更新以测试不同状态的HTTP回调
            testQIS.QIS_Status__c = '承認済';
            update testQIS;

            Test.stopTest();

            // 验证最终记录状态
            testQIS = [SELECT Id, QIS_Status__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals('承認済', testQIS.QIS_Status__c, 'Final status should be 承認済');
        }
    }

    @isTest
    static void testQISAfterDelete() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        System.runAs(testUser) {
            // 创建On_Call记录
            On_Call__c testOnCall = new On_Call__c(
                Name = 'TEST-ONCALL-001',
                OwnerId = testUser.Id
            );
            insert testOnCall;

            // 创建QIS记录并关联On_Call
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-DELETE',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                Source_OnCall__c = testOnCall.Id,
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            Test.startTest();
            insert testQIS;

            // 测试删除QIS记录，这会触发after delete逻辑
            delete testQIS;
            Test.stopTest();

            // 验证记录已被删除
            List<QIS_Report__c> deletedQIS = [SELECT Id FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals(0, deletedQIS.size(), 'QIS record should be deleted');
        }
    }

    @isTest
    static void testQISWorkdayLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        // 创建3个工作日的日历数据
        List<OlympusCalendar__c> workdays = new List<OlympusCalendar__c>();
        for(Integer i = 0; i < 3; i++) {
            workdays.add(new OlympusCalendar__c(
                Date__c = Date.today().addDays(i),
                IsWorkDay__c = 1
            ));
        }
        insert workdays;

        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-WORKDAY',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                PAE_AE_DeadLine_Date__c = null,
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            Test.startTest();
            insert testQIS;

            // 单个记录更新，触发工作日逻辑
            testQIS.Damage_For_Doc_Or_Pat__c = '无';
            update testQIS;
            Test.stopTest();

            // 验证PAE_AE_DeadLine_Date__c被设置
            testQIS = [SELECT Id, PAE_AE_DeadLine_Date__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals(workdays[2].Date__c, testQIS.PAE_AE_DeadLine_Date__c, 'Deadline should be set to third workday');
        }
    }

    @isTest
    static void testQISContractLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        // 创建测试Account和Opportunity数据
        Account testAccount = new Account(
            Name = 'Test Contract Account',
            RecordTypeId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account'
                          AND DeveloperName = 'AgencyContract' LIMIT 1].Id,
            Management_Code__c = 'TEST-MGMT-001'
        );
        insert testAccount;

        Opportunity testOpp = new Opportunity(
            Name = 'Test Opportunity',
            AccountId = testAccount.Id,
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(30)
        );
        insert testOpp;

        Statu_Achievements__c testContract = new Statu_Achievements__c(
            ContractNO__c = 'TEST-CONTRACT-001',
            Opportunity__c = testOpp.Id
        );
        insert testContract;

        System.runAs(testUser) {
            Test.startTest();

            // 创建耗材类型的QIS记录
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-CONTRACT',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                capital_or_consumable__c = '耗材',
                contract_number__c = 'TEST-CONTRACT-001',
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            insert testQIS;

            // 更新合同编号以触发管理编码逻辑
            testQIS.contract_number__c = 'TEST-CONTRACT-002';
            update testQIS;

            Test.stopTest();

            // 验证记录创建成功
            testQIS = [SELECT Id, Managementtext__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertNotEquals(null, testQIS.Id, 'QIS record should be created');
        }
    }

    @isTest
    static void testQISRCChangeLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];
        User testUser2 = new User(
            Test_staff__c = true,
            LastName = 'TestUser2',
            Alias = 'tuser2',
            Email = '<EMAIL>',
            Username = '<EMAIL>' + System.currentTimeMillis(),
            CommunityNickname = 'TestUser2' + System.currentTimeMillis(),
            EmailEncodingKey = 'ISO-2022-JP',
            TimeZoneSidKey = 'Asia/Tokyo',
            LocaleSidKey = 'ja_JP',
            LanguageLocaleKey = 'ja',
            ProfileId = UserInfo.getProfileId(),
            Dept__c = '医疗华北营业本部',
            Province__c = '北京市',
            Job_Category__c = '销售服务'
        );
        insert testUser2;

        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-RC-CHANGE',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            Test.startTest();
            insert testQIS;

            // 更改RC__c以触发审批管理员设置逻辑
            testQIS.RC__c = testUser2.Id;
            update testQIS;
            Test.stopTest();

            // 验证RC变更成功
            testQIS = [SELECT Id, RC__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals(testUser2.Id, testQIS.RC__c, 'RC should be updated');
        }
    }

    @isTest
    static void testQISRepairSubOrderLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        // 创建RepairSubOrder记录
        RepairSubOrder__c testSubOrder = new RepairSubOrder__c(
            Name = 'TEST-SUBORDER-001',
            Status__c = '待处理'
        );
        insert testSubOrder;

        System.runAs(testUser) {
            Test.startTest();

            // 创建带QisSubOrder__c的QIS记录
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-SUBORDER',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                QisSubOrder__c = testSubOrder.Id,
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            insert testQIS;
            Test.stopTest();

            // 验证RepairSubOrder状态被更新
            testSubOrder = [SELECT Id, Status__c FROM RepairSubOrder__c WHERE Id = :testSubOrder.Id];
            System.assertEquals('已转QIS', testSubOrder.Status__c, 'SubOrder status should be updated');
        }
    }

    @isTest
    static void testQISETEmailLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        // 创建测试产品
        Product2 testProduct = new Product2(
            Name = 'Test ET Product',
            IsActive = true,
            Category2__c = '耗材',
            Family = 'ET'
        );
        insert testProduct;

        // 创建测试资产
        Asset testAsset = new Asset(
            Name = 'Test ET Asset',
            Product2Id = testProduct.Id,
            Status = '有库存'
        );
        insert testAsset;

        System.runAs(testUser) {
            Test.startTest();

            // 创建带ET邮件标记的QIS记录
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-ET-EMAIL',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                nonyushohin__c = testAsset.Id,
                ET_QIS_SEND_EMAIL__c = true,
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            insert testQIS;

            // 更新ET邮件标记
            testQIS.ET_QIS_SEND_EMAIL__c = false;
            update testQIS;

            Test.stopTest();

            // 验证邮件字段被清空
            testQIS = [SELECT Id, ET_QIS_SEND_EMAIL1__c, ET_QIS_SEND_EMAIL2__c
                      FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals('', testQIS.ET_QIS_SEND_EMAIL1__c, 'Email field should be cleared');
        }
    }

    @isTest
    static void testQISOSHEmailLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        System.runAs(testUser) {
            Test.startTest();

            // 创建符合OSH邮件条件的QIS记录
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-OSH-EMAIL',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                OCM_judgement__c = '质量问题',
                next_action__c = '无偿维修',
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            insert testQIS;

            // 更新为不符合条件的状态
            testQIS.OCM_judgement__c = '非质量问题';
            testQIS.next_action__c = '有偿维修';
            update testQIS;

            Test.stopTest();

            // 验证OSH邮件字段被清空
            testQIS = [SELECT Id, CN_OSH_QIS__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals('', testQIS.CN_OSH_QIS__c, 'OSH email field should be cleared');
        }
    }

    @isTest
    static void testQISFieldMonitoringLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-FIELD-MONITOR',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            Test.startTest();
            insert testQIS;

            // 更新字段以触发字段监控逻辑
            testQIS.Damage_For_Doc_Or_Pat__c = '无';
            testQIS.Relation_With_The_Problem__c = '不知道';
            update testQIS;
            Test.stopTest();

            // 验证字段更新标记日期被设置
            testQIS = [SELECT Id, Field_Update_Flag_Date__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals(Date.today(), testQIS.Field_Update_Flag_Date__c, 'Field update flag date should be set');
        }
    }

    @isTest
    static void testQISPhotoLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-PHOTO',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                Photo_1__c = '<img src="http://test.com/img1.jpg" />',
                Photo_OSH_1__c = '<img src="http://test.com/osh1.jpg" />',
                problem_detail_photo__c = '<img src="http://test.com/detail.jpg" />',
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            Test.startTest();
            insert testQIS;

            // 更新图片字段以触发PDF转换逻辑
            testQIS.Photo_1__c = '<img src="http://test.com/img1_new.jpg" />';
            testQIS.Photo_OSH_1__c = '<img src="http://test.com/osh1_new.jpg" />';
            testQIS.problem_detail_photo__c = '<img src="http://test.com/detail_new.jpg" />';
            update testQIS;
            Test.stopTest();

            // 验证记录更新成功
            testQIS = [SELECT Id, Photo_1__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertNotEquals(null, testQIS.Photo_1__c, 'Photo field should be updated');
        }
    }

    @isTest
    static void testQISStatusChangeLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        // 创建测试医院和科室
        Account testHospital = new Account(
            RecordTypeId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account'
                          AND DeveloperName = 'HP' LIMIT 1].Id,
            Name = 'Test Hospital Status',
            Account_Share__c = false
        );
        insert testHospital;

        Account testDepartment = new Account(
            RecordTypeId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account'
                          AND DeveloperName = 'Department_OTH' LIMIT 1].Id,
            Name = 'Test Department Status',
            ParentId = testHospital.Id,
            Hospital__c = testHospital.Id,
            Account_Share__c = false
        );
        insert testDepartment;

        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-STATUS-CHANGE',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                QIS_Status__c = '草案中',
                Hospital__c = testHospital.Id,
                Hospital_Department__c = testDepartment.Id,
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            Test.startTest();
            insert testQIS;

            // 更改状态从草案中到其他状态，触发Account更新逻辑
            testQIS.QIS_Status__c = '申請中';
            update testQIS;
            Test.stopTest();

            // 验证Account的Account_Share__c被更新
            testHospital = [SELECT Id, Account_Share__c FROM Account WHERE Id = :testHospital.Id];
            System.assertEquals(true, testHospital.Account_Share__c, 'Hospital Account_Share should be updated');
        }
    }

    @isTest
    static void testQISCancelStatusLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-CANCEL',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                QIS_Status__c = '申請中',
                otherinformation__c = '初始信息',
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            Test.startTest();
            insert testQIS;

            // 更改状态为取消，触发其他信息字段更新
            testQIS.QIS_Status__c = '取消';
            update testQIS;
            Test.stopTest();

            // 验证其他信息字段被更新
            testQIS = [SELECT Id, otherinformation__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assert(testQIS.otherinformation__c.contains('案件已经取消'), 'Other information should contain cancel message');
        }
    }

    @isTest
    static void testQISAwareDateLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-AWARE-DATE',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                AwareDate__c = Date.newInstance(2021, 10, 1),
                Aware_date__c = Date.newInstance(2021, 9, 15),
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            Test.startTest();
            insert testQIS;

            // 更新AwareDate以触发日期同步逻辑
            testQIS.AwareDate__c = Date.newInstance(2021, 11, 1);
            update testQIS;
            Test.stopTest();

            // 验证Aware_date__c被同步
            testQIS = [SELECT Id, AwareDate__c, Aware_date__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals(testQIS.AwareDate__c, testQIS.Aware_date__c, 'Aware dates should be synchronized');
        }
    }

    @isTest
    static void testQISProfileLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-PROFILE',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today().addDays(1), // 未来日期以触发Profile逻辑
                IS_AE__c = 'YES',
                IS_PAE__c = 'YES',
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            Test.startTest();
            insert testQIS;
            Test.stopTest();

            // 验证Profile字段被设置
            testQIS = [SELECT Id, isAE_Profile__c, isPAE_Profile__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertEquals('YES', testQIS.isAE_Profile__c, 'AE Profile should be set');
            System.assertEquals('YES', testQIS.isPAE_Profile__c, 'PAE Profile should be set');
        }
    }

    @isTest
    static void testQISSalesdepartmentLogic() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        System.runAs(testUser) {
            QIS_Report__c testQIS = new QIS_Report__c(
                RC__c = testUser.Id,
                Name = 'QIS-TEST-SALES-DEPT',
                BusinessAssistantNo__c = testUser.Id,
                QIS_Submit_day__c = Date.today(),
                Salesdepartment__c = '华东',
                Damage_For_Doc_Or_Pat__c = '有',
                Relation_With_The_Problem__c = '有可能',
                Report_For_Goz__c = '不知道'
            );

            Test.startTest();
            insert testQIS;

            // 更新销售部门以触发邮件设置逻辑
            testQIS.Salesdepartment__c = '东北';
            update testQIS;

            // 再次更新为西北
            testQIS.Salesdepartment__c = '西北';
            update testQIS;

            // 再次更新为华南
            testQIS.Salesdepartment__c = '华南';
            update testQIS;
            Test.stopTest();

            // 验证邮件提醒字段被设置
            testQIS = [SELECT Id, Not_Get_Asset_Remind__c FROM QIS_Report__c WHERE Id = :testQIS.Id];
            System.assertNotEquals(null, testQIS.Not_Get_Asset_Remind__c, 'Email reminder should be set');
        }
    }

    @isTest
    static void testQISMultipleRecordsUpdate() {
        User testUser = [SELECT Id FROM User WHERE LastName = 'TestUser' LIMIT 1];

        System.runAs(testUser) {
            List<QIS_Report__c> qisList = new List<QIS_Report__c>();

            // 创建多个QIS记录
            for(Integer i = 0; i < 3; i++) {
                qisList.add(new QIS_Report__c(
                    RC__c = testUser.Id,
                    Name = 'QIS-TEST-MULTI-' + i,
                    BusinessAssistantNo__c = testUser.Id,
                    QIS_Submit_day__c = Date.today(),
                    Damage_For_Doc_Or_Pat__c = '有',
                    Relation_With_The_Problem__c = '有可能',
                    Report_For_Goz__c = '不知道'
                ));
            }

            Test.startTest();
            insert qisList;

            // 批量更新记录
            for(QIS_Report__c qis : qisList) {
                qis.Damage_For_Doc_Or_Pat__c = '无';
            }
            update qisList;
            Test.stopTest();

            // 验证所有记录都被更新
            List<QIS_Report__c> updatedQIS = [SELECT Id, Damage_For_Doc_Or_Pat__c
                                             FROM QIS_Report__c WHERE Id IN :qisList];
            for(QIS_Report__c qis : updatedQIS) {
                System.assertEquals('无', qis.Damage_For_Doc_Or_Pat__c, 'All records should be updated');
            }
        }
    }

    //     QIS_Report__c qr = new QIS_Report__c(
    //         RC__c = UserInfo.getUserId(),
    //         Photo_1__c = '<img src="http://www.google.co.jp/img1" />',
    //         Photo_2__c = '<img src="http://www.google.co.jp/img2" />',
    //         Photo_3__c = '<img src="http://www.google.co.jp/img3" />',
    //         Photo_4__c = '<img src="http://www.google.co.jp/img4" />',
    //         Photo_5__c = '<img src="http://www.google.co.jp/img5" />',
    //         Photo_6__c = '<img src="http://www.google.co.jp/img6" />',
    //         Photo_7__c = '<img src="http://www.google.co.jp/img7" />',
    //         Photo_8__c = '<img src="http://www.google.co.jp/img8" />',
    //         Damage_For_Doc_Or_Pat__c = '有',
    //         Relation_With_The_Problem__c = '有可能',
    //         Report_For_Goz__c = '不知道',
    //         Name = 'QIS012345',
    //         BusinessAssistantNo__c = UserInfo.getUserId(),
    //         QIS_Submit_day__c = Date.Today()
    //     );
    //     insert qr;
    //     System.Test.startTest();
        
    //     qr = [select Photo_1_Text__c, Photo_2_Text__c, Photo_3_Text__c, Photo_4_Text__c,Photo_5_Text__c, Photo_6_Text__c, Photo_7_Text__c, Photo_8_Text__c from QIS_Report__c where Id = :qr.Id];
    //     System.assertNotEquals(null, qr.Photo_1_Text__c);
    //     System.assertNotEquals(null, qr.Photo_2_Text__c);
    //     System.assertNotEquals(null, qr.Photo_3_Text__c);
    //     System.assertNotEquals(null, qr.Photo_4_Text__c);
    //     System.assertNotEquals(null, qr.Photo_5_Text__c);
    //     System.assertNotEquals(null, qr.Photo_6_Text__c);
    //     System.assertNotEquals(null, qr.Photo_7_Text__c);
    //     System.assertNotEquals(null, qr.Photo_8_Text__c);
    //     System.Test.stopTest();
    // }

    // static testMethod void triggerTest() {
    //     User us = new User(Test_staff__c = true, LastName = 'TestMao2', 
    //             Alias = 'hp2', CommunityNickname = 'TestMao2', Email = '<EMAIL>',
    //             Username = '<EMAIL>', IsActive = true, EmailEncodingKey = 'ISO-2022-JP',
    //             TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja',
    //             ProfileId = System.Label.ProfileId_SystemAdmin,
    //             Dept__c = '医疗华北营业本部', Province__c = '北京市',Job_Category__c='销售服务');
    //     insert us;
    //     User hpOwner = new User(Test_staff__c = true, LastName = 'TestMao1',
    //             Alias = 'hp1', CommunityNickname = 'TestMao1', Email = '<EMAIL>',
    //             Username = '<EMAIL>', IsActive = true, EmailEncodingKey = 'ISO-2022-JP',
    //             TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja',
    //             ProfileId = System.Label.ProfileId_SystemAdmin,
    //             Dept__c = '医疗华北营业本部', Province__c = '北京市',Job_Category__c='销售市场');
    //     insert hpOwner;
    //     System.runAs(us) {
    //         OlympusCalendar__c oly1 = new OlympusCalendar__c(Date__c=Date.valueOf('2030-12-17'));
    //         insert oly1;
    //         OlympusCalendar__c oly2 = new OlympusCalendar__c(Date__c=Date.valueOf('2030-12-17'));
    //         insert oly2;
    //         OlympusCalendar__c oly3 = new OlympusCalendar__c(Date__c=Date.valueOf('2030-12-17'));
    //         insert oly3;

    //         QIS_Report__c qr = new QIS_Report__c(
    //             RC__c = hpOwner.Id,
    //             Photo_1__c = '<img src="http://www.google.co.jp/img1" />',
    //             Photo_2__c = '<img src="http://www.google.co.jp/img2" />',
    //             Photo_3__c = '<img src="http://www.google.co.jp/img3" />',
    //             Photo_4__c = '<img src="http://www.google.co.jp/img4" />',
    //             Photo_5__c = '<img src="http://www.google.co.jp/img5" />',
    //             Photo_6__c = '<img src="http://www.google.co.jp/img6" />',
    //             Photo_7__c = '<img src="http://www.google.co.jp/img7" />',
    //             Photo_8__c = '<img src="http://www.google.co.jp/img8" />',
    //             Damage_For_Doc_Or_Pat__c = '有',
    //             Relation_With_The_Problem__c = '有可能',
    //             Report_For_Goz__c = '不知道',
    //             Name = 'QIS012345',
    //             BusinessAssistantNo__c = us.Id,
    //             QIS_Submit_day__c = Date.Today()
    //         );
    //         insert qr;

    //         qr.RC__c = us.Id;
    //         update qr;

    //         System.Test.startTest();

    //         us.Province__c = '河南省';
    //         update us;
    //         update qr;

    //         us.Province__c = '辽宁省';
    //         update us;
    //         update qr;

    //         us.Province__c = '海南省';
    //         update us;
    //         update qr;

    //         us.Province__c = '四川省';
    //         update us;
    //         update qr;

    //         us.Province__c = '安徽省';
    //         update us;
    //         //update qr;
    //         System.Test.stopTest();
    //     }
    // }
    //2020/11/20 songxiaoqi  start
    // static testMethod void ETcTest(){
    //     User us = new User(Test_staff__c = true, LastName = 'TestMao2',
    //             Alias = 'hp2', CommunityNickname = 'TestMao2', Email = '<EMAIL>',
    //             Username = '<EMAIL>', IsActive = true, EmailEncodingKey = 'ISO-2022-JP',
    //             TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja',
    //             ProfileId = System.Label.ProfileId_SystemAdmin,
    //             Dept__c = '医疗华北营业本部', Province__c = '北京市',Job_Category__c='销售服务');
    //     insert us;
    //     User hpOwner = new User(Test_staff__c = true, LastName = 'TestMao1', 
    //             Alias = 'hp1', CommunityNickname = 'TestMao1', Email = '<EMAIL>',
    //             Username = '<EMAIL>', IsActive = true, EmailEncodingKey = 'ISO-2022-JP',
    //             TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja',
    //             ProfileId = System.Label.ProfileId_SystemAdmin,
    //             Dept__c = '医疗华北营业本部', Province__c = '北京市',Job_Category__c='销售市场');
    //     insert hpOwner;
    //     System.runAs(us) {
    //         OlympusCalendar__c oly1 = new OlympusCalendar__c(Date__c=Date.valueOf('2030-12-17'));
    //         insert oly1;
    //         OlympusCalendar__c oly2 = new OlympusCalendar__c(Date__c=Date.valueOf('2030-12-17'));
    //         insert oly2;
    //         OlympusCalendar__c oly3 = new OlympusCalendar__c(Date__c=Date.valueOf('2030-12-17'));
    //         insert oly3;

    //         QIS_Report__c qr = new QIS_Report__c(
    //             RC__c = hpOwner.Id,
    //             Photo_1__c = '<img src="http://www.google.co.jp/img1" />',
    //             Photo_2__c = '<img src="http://www.google.co.jp/img2" />',
    //             Photo_3__c = '<img src="http://www.google.co.jp/img3" />',
    //             Photo_4__c = '<img src="http://www.google.co.jp/img4" />',
    //             Photo_5__c = '<img src="http://www.google.co.jp/img5" />',
    //             Photo_6__c = '<img src="http://www.google.co.jp/img6" />',
    //             Photo_7__c = '<img src="http://www.google.co.jp/img7" />',
    //             Photo_8__c = '<img src="http://www.google.co.jp/img8" />',
    //             Damage_For_Doc_Or_Pat__c = '有',
    //             Relation_With_The_Problem__c = '有可能',
    //             Report_For_Goz__c = '不知道',
    //             Name = 'QIS012345',
    //             contract_number_ET__c = 'BJ-GYN-152553',
    //             BusinessAssistantNo__c = us.Id,
    //             ET_QIS_SEND_EMAIL1__c = '<EMAIL>',
    //             ET_QIS_SEND_EMAIL2__c = '<EMAIL>',
    //             ET_QIS_SEND_EMAIL3__c = '<EMAIL>',
    //             ET_QIS_SEND_EMAIL4__c = '<EMAIL>',
    //             QIS_Submit_day__c = Date.Today()
    //         );
    //         insert qr;

           
    //         qr.RC__c = us.Id;
    //         qr.Photo_1__c = '<img src="http://www.google.co.jp/img11" />';
    //         qr.Photo_2__c = '<img src="http://www.google.co.jp/img21" />';
    //         qr.Photo_3__c = '<img src="http://www.google.co.jp/img31" />';
    //         qr.Photo_4__c = '<img src="http://www.google.co.jp/img41" />';
    //         qr.Photo_5__c = '<img src="http://www.google.co.jp/img51" />';
    //         qr.Photo_6__c = '<img src="http://www.google.co.jp/img61" />';
    //         qr.Photo_7__c = '<img src="http://www.google.co.jp/img71" />';
    //         qr.Photo_8__c = '<img src="http://www.google.co.jp/img81" />';
    //         // qr.Photo_OSH_8__c = '<img src="http://www.google.co.jp/img81" />';

            
    //         us.Province__c = '河南省';
    //         update us;
    //          System.Test.startTest();
    //         update qr;
    //         System.Test.stopTest();
    //     }
    // //2020/11/20 songxiaoqi end
    //wangweipeng        LJPH-C7ZBSE          2021/10/27              start
    @isTest
    static void triggerTest2() {
        isTest = true;
        Oly_TriggerHandler.bypass('ContactTriggerHandler');
        // 省
        Address_Level__c al = new Address_Level__c();
        al.Name = '東京';
        al.Level1_Code__c = 'CN-99';
        al.Level1_Sys_No__c = '999999';
        insert al;
        // 市
        Address_Level2__c al2 = new Address_Level2__c();
        al2.Level1_Code__c = 'CN-99';
        al2.Level1_Sys_No__c = '999999';
        al2.Level1_Name__c = '東京';
        al2.Name = '渋谷区';
        al2.Level2_Code__c = 'CN-9999';
        al2.Level2_Sys_No__c = '9999999';
        al2.Address_Level__c = al.id;
        insert al2;

        // 病院を作る
        Account hospital = new Account();
        hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
        hospital.Name = 'test hospital';
        hospital.Is_Active__c = '有効';
        hospital.Attribute_Type__c = '卫生部';
        hospital.Speciality_Type__c = '综合医院';
        hospital.Grade__c = '一级';
        hospital.OCM_Category__c = 'SLTV';
        hospital.Is_Medical__c = '医疗机构';
        hospital.State_Master__c = al.id;
        hospital.City_Master__c = al2.id;
        hospital.Town__c = '东京';
        insert hospital;

        // 戦略科室を得る
        Account[] strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_OTH'];
        // 診療科を作る
        Account dep = new Account();
        dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
        dep.Name = 'test dep';
        dep.AgentCode_Ext__c = '9999998';
        dep.ParentId = strategicDep[0].Id;
        dep.Department_Class__c = strategicDep[0].Id;
        dep.Hospital__c = hospital.Id;
        insert dep;

        Contact contact2 = new Contact();
        contact2.AccountId = dep.Id;
        contact2.LastName = 'test1经销商';
        insert contact2;

        // 产品
        Product2 pro5 = new Product2(Name='name05',IsActive=true,
                Fixture_Model_No__c='n05',Serial_Lot_No__c='S/N tracing',
                Fixture_Model_No_T__c = 'n05',
                Category2__c='耗材',
                Family='ENG',
                ProductCode_Ext__c='pc05',Manual_Entry__c=false);
        insert pro5;

        // 保有设备C (只有附属品 个体管理)
        Asset assetC1 = new Asset(Asset_Owner__c = 'Olympus');
        assetC1.SerialNumber = 'assetC1';
        assetC1.Name = 'assetC1';
        assetC1.AccountId = dep.Id;
        assetC1.Department_Class__c = strategicDep[0].Id;
        assetC1.Hospital__c = hospital.Id;
        assetC1.Product2Id = pro5.Id;
        assetC1.Quantity = 1;
        assetC1.Status = '有库存';
        assetC1.Manage_type__c = '个体管理';
        assetC1.Internal_asset_location__c = '北京 备品中心';
        assetC1.Loaner_accsessary__c = true;
        assetC1.Delete_Flag__c = false;
        assetC1.Freeze_sign__c = false;
        assetC1.Out_of_wh__c = 0;

        insert assetC1;
        

        QIS_Report__c qr = new QIS_Report__c(
            RC__c = UserInfo.getUserId(),
            Name = 'QIS012345',
            nonyushohin__c=assetC1.id,
            QIS_Submit_day__c = Date.Today()
        );
        Test.startTest();
        insert qr;
        Test.stopTest();
    }
    //wangweipeng        LJPH-C7ZBSE          2021/10/27              end
}